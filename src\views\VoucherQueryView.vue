<template>
  <div class="voucher-query-view">
    <div class="section-header">
      <h2>凭证查询系统</h2>
      <p>可视化复杂类别组成的凭证查询工具</p>
    </div>

    <!-- 查询面板容器 -->
    <div class="query-panels-container">
      <div class="panels-header">
        <h3>查询面板</h3>
        <div class="panel-controls">
          <el-button type="primary" @click="addQueryPanel" :icon="Plus">
            添加面板
          </el-button>
          <el-button type="success" @click="loadSampleQuery" :icon="Search">
            加载示例查询
          </el-button>
          <el-radio-group v-model="panelLogic" class="panel-logic">
            <el-radio-button label="AND">面板间 AND</el-radio-button>
            <el-radio-button label="OR">面板间 OR</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 查询面板列表 -->
      <div class="query-panels">
        <div 
          v-for="(panel, index) in queryPanels" 
          :key="panel.id"
          class="query-panel"
        >
          <div class="panel-header">
            <span class="panel-title">查询面板 {{ index + 1 }}</span>
            <div class="panel-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="addCondition(panel.id)"
                :icon="Plus"
              >
                添加条件
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="removePanel(panel.id)"
                :icon="Delete"
                v-if="queryPanels.length > 1"
              >
                删除面板
              </el-button>
            </div>
          </div>

          <!-- 条件列表 -->
          <div class="conditions-container">
            <div 
              v-for="(condition, condIndex) in panel.conditions" 
              :key="condition.id"
              class="condition-row"
            >
              <!-- 逻辑连接符 -->
              <div class="logic-operator" v-if="condIndex > 0">
                <el-select v-model="condition.logic" placeholder="逻辑">
                  <el-option label="AND" value="AND" />
                  <el-option label="OR" value="OR" />
                </el-select>
              </div>

              <!-- 字段选择 -->
              <div class="field-select">
                <el-select v-model="condition.field" placeholder="选择字段" @change="onFieldChange(condition)">
                  <el-option 
                    v-for="field in queryFields" 
                    :key="field.value" 
                    :label="field.label" 
                    :value="field.value"
                  />
                </el-select>
              </div>

              <!-- 操作符选择 -->
              <div class="operator-select">
                <el-select v-model="condition.operator" placeholder="操作符" @change="onOperatorChange(condition)">
                  <el-option
                    v-for="op in getOperatorsForField(condition.field)"
                    :key="op.value"
                    :label="op.label"
                    :value="op.value"
                  />
                </el-select>
              </div>

              <!-- 值输入 -->
              <div class="value-input">
                <component
                  :is="getInputComponent(condition)"
                  v-model="condition.value"
                  :placeholder="getPlaceholder(condition)"
                  :disabled="condition.operator === 'is_null' || condition.operator === 'is_not_null'"
                  v-bind="getInputProps(condition)"
                />
              </div>

              <!-- 删除条件按钮 -->
              <div class="condition-actions">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeCondition(panel.id, condition.id)"
                  :icon="Delete"
                  v-if="panel.conditions.length > 1"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <el-button 
        type="primary" 
        size="large" 
        @click="executeQuery"
        :loading="loading"
        :icon="Search"
      >
        独立筛选查询
      </el-button>
      <el-button 
        type="success" 
        size="large" 
        @click="exportAllVouchers"
        :loading="exportLoading"
        :icon="Download"
      >
        全凭证导出
      </el-button>
    </div>

    <!-- 查询结果展示 -->
    <div class="results-container" v-if="hasResults">
      <div class="results-header">
        <h3>查询结果</h3>
        <span class="result-count">共找到 {{ resultCount }} 条记录</span>
      </div>
      
      <div class="table-container">
        <VTableComponent
          :data="formattedTableData"
          :height="500"
          :width="1200"
          @cell-click="handleCellClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete, Search, Download } from '@element-plus/icons-vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const hasResults = ref(false)
const resultCount = ref(0)
const panelLogic = ref('AND')
const queryPanels = ref([])
const tableData = ref([])
const tableColumns = ref([])

// 格式化表格数据为二维数组
const formattedTableData = computed(() => {
  if (tableData.value.length === 0) return []

  // 表头
  const headers = tableColumns.value.map(col => col.title)

  // 数据行
  const rows = tableData.value.map(row => {
    return tableColumns.value.map(col => row[col.field] || '')
  })

  return [headers, ...rows]
})

// 查询字段配置
const queryFields = ref([
  { label: '开始日期', value: 'start_date', type: 'date' },
  { label: '结束日期', value: 'end_date', type: 'date' },
  { label: '事由', value: 'reason', type: 'text' },
  { label: '总账科目长文本', value: 'account_text', type: 'text' },
  { label: '合同编号', value: 'contract_number', type: 'text' },
  { label: '凭证编号', value: 'voucher_number', type: 'text' },
  { label: '中台单据号', value: 'platform_doc_number', type: 'text' },
  { label: '金额区间最小值', value: 'amount_min', type: 'number' },
  { label: '金额区间最大值', value: 'amount_max', type: 'number' }
])

// 操作符配置
const operators = {
  text: [
    { label: '等于', value: 'equals' },
    { label: '包含', value: 'contains' },
    { label: '开始于', value: 'starts_with' },
    { label: '结束于', value: 'ends_with' },
    { label: '不等于', value: 'not_equals' },
    { label: '为空', value: 'is_null' },
    { label: '不为空', value: 'is_not_null' }
  ],
  number: [
    { label: '等于', value: 'equals' },
    { label: '大于', value: 'greater_than' },
    { label: '小于', value: 'less_than' },
    { label: '大于等于', value: 'greater_equal' },
    { label: '小于等于', value: 'less_equal' },
    { label: '不等于', value: 'not_equals' },
    { label: '为空', value: 'is_null' },
    { label: '不为空', value: 'is_not_null' }
  ],
  date: [
    { label: '等于', value: 'equals' },
    { label: '大于', value: 'greater_than' },
    { label: '小于', value: 'less_than' },
    { label: '大于等于', value: 'greater_equal' },
    { label: '小于等于', value: 'less_equal' },
    { label: '不等于', value: 'not_equals' },
    { label: '为空', value: 'is_null' },
    { label: '不为空', value: 'is_not_null' }
  ]
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 创建新的查询面板
function createNewPanel() {
  return {
    id: generateId(),
    conditions: [createNewCondition()]
  }
}

// 创建新的查询条件
function createNewCondition() {
  return {
    id: generateId(),
    field: '',
    operator: '',
    value: '',
    logic: 'AND'
  }
}

// 添加查询面板
function addQueryPanel() {
  queryPanels.value.push(createNewPanel())
}

// 删除查询面板
function removePanel(panelId) {
  const index = queryPanels.value.findIndex(panel => panel.id === panelId)
  if (index > -1) {
    queryPanels.value.splice(index, 1)
  }
}

// 添加查询条件
function addCondition(panelId) {
  const panel = queryPanels.value.find(p => p.id === panelId)
  if (panel) {
    panel.conditions.push(createNewCondition())
  }
}

// 删除查询条件
function removeCondition(panelId, conditionId) {
  const panel = queryPanels.value.find(p => p.id === panelId)
  if (panel) {
    const index = panel.conditions.findIndex(c => c.id === conditionId)
    if (index > -1) {
      panel.conditions.splice(index, 1)
    }
  }
}

// 获取字段对应的操作符
function getOperatorsForField(fieldValue) {
  const field = queryFields.value.find(f => f.value === fieldValue)
  return field ? operators[field.type] || [] : []
}

// 字段变化处理
function onFieldChange(condition) {
  condition.operator = ''
  condition.value = ''
}

// 操作符变化处理
function onOperatorChange(condition) {
  // 如果选择了"为空"或"不为空"，清空值
  if (condition.operator === 'is_null' || condition.operator === 'is_not_null') {
    condition.value = ''
  }
}

// 获取输入组件
function getInputComponent(condition) {
  const field = queryFields.value.find(f => f.value === condition.field)
  if (!field) return 'el-input'
  
  switch (field.type) {
    case 'date':
      return 'el-date-picker'
    case 'number':
      return 'el-input-number'
    default:
      return 'el-input'
  }
}

// 获取输入组件属性
function getInputProps(condition) {
  const field = queryFields.value.find(f => f.value === condition.field)
  if (!field) return {}
  
  switch (field.type) {
    case 'date':
      return {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    case 'number':
      return {
        precision: 2,
        step: 0.01
      }
    default:
      return {}
  }
}

// 获取占位符文本
function getPlaceholder(condition) {
  const field = queryFields.value.find(f => f.value === condition.field)
  if (!field) return '请输入值'
  
  if (condition.operator === 'is_null' || condition.operator === 'is_not_null') {
    return '无需输入值'
  }
  
  return `请输入${field.label}`
}

// 执行查询
async function executeQuery() {
  // 验证查询条件
  if (!validateQuery()) {
    return
  }
  
  loading.value = true
  
  try {
    // 构建查询参数
    const queryParams = {
      panels: queryPanels.value,
      panelLogic: panelLogic.value
    }
    
    // 这里应该调用实际的API
    // const response = await fetch('/api/voucher-query', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(queryParams)
    // })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟查询结果
    const mockData = generateMockVoucherData()
    tableData.value = mockData
    tableColumns.value = getVoucherColumns()
    resultCount.value = mockData.length
    hasResults.value = true
    
    ElMessage.success(`查询完成，共找到 ${mockData.length} 条记录`)
    
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

// 验证查询条件
function validateQuery() {
  for (const panel of queryPanels.value) {
    for (const condition of panel.conditions) {
      if (!condition.field) {
        ElMessage.warning('请选择查询字段')
        return false
      }
      if (!condition.operator) {
        ElMessage.warning('请选择操作符')
        return false
      }
      if (!condition.value && condition.operator !== 'is_null' && condition.operator !== 'is_not_null') {
        ElMessage.warning('请输入查询值')
        return false
      }
    }
  }
  return true
}

// 全凭证导出
async function exportAllVouchers() {
  exportLoading.value = true
  
  try {
    // 这里应该调用实际的导出API
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('导出完成')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 生成模拟凭证数据
function generateMockVoucherData() {
  const data = []
  const reasons = ['材料采购', '工程款支付', '设备租赁', '人工费用', '管理费用', '财务费用', '销售费用', '其他费用']
  const accounts = ['原材料', '应付账款', '银行存款', '应收账款', '固定资产', '累计折旧', '主营业务收入', '主营业务成本']

  for (let i = 1; i <= 50; i++) {
    const randomReason = reasons[Math.floor(Math.random() * reasons.length)]
    const randomAccount = accounts[Math.floor(Math.random() * accounts.length)]

    data.push({
      id: i,
      voucher_number: `PZ${String(i).padStart(6, '0')}`,
      date: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      reason: `${randomReason}-${i}`,
      account_text: `${randomAccount}-详细科目${i}`,
      contract_number: `HT${String(i).padStart(8, '0')}`,
      platform_doc_number: `ZT${String(i).padStart(10, '0')}`,
      amount: (Math.random() * 1000000).toFixed(2),
      debit_amount: (Math.random() * 500000).toFixed(2),
      credit_amount: (Math.random() * 500000).toFixed(2)
    })
  }
  return data
}

// 获取凭证表格列配置
function getVoucherColumns() {
  return [
    { field: 'voucher_number', title: '凭证编号', width: 120 },
    { field: 'date', title: '日期', width: 100 },
    { field: 'reason', title: '事由', width: 200 },
    { field: 'account_text', title: '总账科目长文本', width: 200 },
    { field: 'contract_number', title: '合同编号', width: 150 },
    { field: 'platform_doc_number', title: '中台单据号', width: 150 },
    { field: 'amount', title: '金额', width: 120 },
    { field: 'debit_amount', title: '借方金额', width: 120 },
    { field: 'credit_amount', title: '贷方金额', width: 120 }
  ]
}

// 处理单元格点击
function handleCellClick(params) {
  console.log('Cell clicked:', params)
}

// 加载示例查询
function loadSampleQuery() {
  // 清空现有面板
  queryPanels.value = []

  // 创建示例查询面板
  const samplePanel = {
    id: generateId(),
    conditions: [
      {
        id: generateId(),
        field: 'start_date',
        operator: 'greater_equal',
        value: '2024-01-01',
        logic: 'AND'
      },
      {
        id: generateId(),
        field: 'end_date',
        operator: 'less_equal',
        value: '2024-12-31',
        logic: 'AND'
      },
      {
        id: generateId(),
        field: 'reason',
        operator: 'contains',
        value: '材料',
        logic: 'AND'
      }
    ]
  }

  queryPanels.value.push(samplePanel)
  ElMessage.success('已加载示例查询条件')
}

// 组件挂载时初始化
onMounted(() => {
  // 添加初始查询面板
  addQueryPanel()
})
</script>

<style scoped>
.voucher-query-view {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.section-header {
  margin-bottom: 30px;
  text-align: center;
}

.section-header h2 {
  color: #1967d2;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.section-header p {
  color: #666;
  font-size: 16px;
}

.query-panels-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panels-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e8f0fe;
}

.panels-header h3 {
  color: #1967d2;
  font-size: 20px;
  margin: 0;
}

.panel-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.panel-logic {
  margin-left: 16px;
}

.query-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.query-panel {
  border: 2px solid #e8f0fe;
  border-radius: 8px;
  padding: 16px;
  background: #fafbfc;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.panel-title {
  font-weight: 600;
  color: #1967d2;
  font-size: 16px;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.conditions-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.logic-operator {
  min-width: 80px;
}

.field-select {
  min-width: 180px;
}

.operator-select {
  min-width: 120px;
}

.value-input {
  flex: 1;
  min-width: 200px;
}

.condition-actions {
  min-width: 40px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 30px 0;
}

.action-buttons .el-button {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
}

.results-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e8f0fe;
}

.results-header h3 {
  color: #1967d2;
  font-size: 20px;
  margin: 0;
}

.result-count {
  color: #666;
  font-size: 14px;
  background: #e8f0fe;
  padding: 4px 12px;
  border-radius: 16px;
}

.table-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .condition-row {
    flex-wrap: wrap;
    gap: 8px;
  }

  .field-select,
  .operator-select,
  .value-input {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .voucher-query-view {
    padding: 12px;
  }

  .panels-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .panel-controls {
    justify-content: space-between;
  }

  .condition-row {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
