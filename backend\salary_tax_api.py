from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import duckdb
from datetime import datetime
import json
import pandas as pd

router = APIRouter()

# 数据库连接
DB_PATH = "financial_data.duckdb"

# 请求模型
class SalaryTaxFetchRequest(BaseModel):
    filters: Dict[str, Any]
    timestamp: str

class SalaryTaxSaveRequest(BaseModel):
    year: str
    taxDeclaration: List[List[Any]]
    taxCalculation: List[List[Any]]
    specialDeduction: List[List[Any]]
    remoteTax: List[List[Any]]
    idCard: List[List[Any]]
    projectMapping: List[List[Any]]
    subsidyPackage: List[List[Any]]
    bonus: List[List[Any]]
    outsourcingSalary: List[List[Any]]
    socialSecurityAndHousingFund: List[List[Any]]
    socialSecurityAndHousingFundAdjustment: List[List[Any]]
    timestamp: str

class SocialSecurityAllocationRequest(BaseModel):
    taxCalculationData: List[List[Any]]
    socialSecurityData: List[List[Any]]
    year: str

class BuildFinanceTemplateRequest(BaseModel):
    adjustmentData: List[List[Any]]
    year: str
    templateType: str

# 响应模型
class SalaryTaxFetchResponse(BaseModel):
    code: int
    message: str
    data: Dict[str, List[List[Any]]]
    timestamp: str

class SalaryTaxSaveResponse(BaseModel):
    code: int
    message: str
    timestamp: str

class SocialSecurityAllocationResponse(BaseModel):
    code: int
    message: str
    data: Optional[List[List[Any]]]
    timestamp: str

class BuildFinanceTemplateResponse(BaseModel):
    code: int
    message: str
    data: Optional[Dict[str, Any]]
    timestamp: str

def init_salary_tax_tables():
    """初始化薪酬个税相关表"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        # 创建薪酬个税数据表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS salary_tax_data (
                id INTEGER PRIMARY KEY,
                year VARCHAR,
                table_name VARCHAR,
                data_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.close()
    except Exception as e:
        print(f"初始化薪酬个税表失败: {e}")

@router.post("/api/salary-tax/fetch-all", response_model=SalaryTaxFetchResponse)
async def fetch_all_salary_tax_data(request: SalaryTaxFetchRequest):
    """拉取所有薪酬个税数据"""
    try:
        # 初始化表
        init_salary_tax_tables()
        
        conn = duckdb.connect(DB_PATH)
        
        fiscal_year = request.filters.get('fiscalYear', '')
        if not fiscal_year:
            raise HTTPException(status_code=400, detail="缺少年份参数")
        
        # 查询指定年份的数据
        query = """
            SELECT table_name, data_json 
            FROM salary_tax_data 
            WHERE year = ? 
            ORDER BY updated_at DESC
        """
        
        result = conn.execute(query, [fiscal_year]).fetchall()
        
        # 构建返回数据
        data = {}
        table_names = [
            'taxDeclaration', 'taxCalculation', 'specialDeduction', 'remoteTax',
            'idCard', 'projectMapping', 'subsidyPackage', 'bonus',
            'outsourcingSalary', 'socialSecurityAndHousingFund', 
            'socialSecurityAndHousingFundAdjustment'
        ]
        
        # 初始化默认数据结构
        for table_name in table_names:
            data[table_name] = get_default_table_data(table_name)
        
        # 更新从数据库获取的数据
        for row in result:
            table_name, data_json = row
            if table_name in data and data_json:
                try:
                    data[table_name] = json.loads(data_json)
                except json.JSONDecodeError:
                    print(f"解析表 {table_name} 数据失败")
        
        conn.close()
        
        return SalaryTaxFetchResponse(
            code=200,
            message=f"成功获取 {fiscal_year} 年度薪酬个税数据",
            data=data,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"拉取数据失败: {str(e)}")

@router.post("/api/salary-tax/save-all", response_model=SalaryTaxSaveResponse)
async def save_all_salary_tax_data(request: SalaryTaxSaveRequest):
    """保存所有薪酬个税数据"""
    try:
        # 初始化表
        init_salary_tax_tables()
        
        conn = duckdb.connect(DB_PATH)
        
        # 准备要保存的数据
        tables_data = {
            'taxDeclaration': request.taxDeclaration,
            'taxCalculation': request.taxCalculation,
            'specialDeduction': request.specialDeduction,
            'remoteTax': request.remoteTax,
            'idCard': request.idCard,
            'projectMapping': request.projectMapping,
            'subsidyPackage': request.subsidyPackage,
            'bonus': request.bonus,
            'outsourcingSalary': request.outsourcingSalary,
            'socialSecurityAndHousingFund': request.socialSecurityAndHousingFund,
            'socialSecurityAndHousingFundAdjustment': request.socialSecurityAndHousingFundAdjustment
        }
        
        # 删除该年份的旧数据
        conn.execute("DELETE FROM salary_tax_data WHERE year = ?", [request.year])
        
        # 插入新数据
        insert_query = """
            INSERT INTO salary_tax_data (year, table_name, data_json, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        saved_count = 0
        for table_name, table_data in tables_data.items():
            if table_data and len(table_data) > 1:  # 确保有数据（除了表头）
                data_json = json.dumps(table_data, ensure_ascii=False)
                conn.execute(insert_query, [request.year, table_name, data_json])
                saved_count += 1
        
        conn.close()
        
        return SalaryTaxSaveResponse(
            code=200,
            message=f"成功保存 {saved_count} 个表格的 {request.year} 年度薪酬个税数据",
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存数据失败: {str(e)}")

def get_default_table_data(table_name: str) -> List[List[Any]]:
    """获取默认表格数据"""
    defaults = {
        'taxDeclaration': [
            ['工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
             '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
             '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
             '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
             '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
             '准予扣除的捐赠额', '减免税额', '备注']
        ],
        'taxCalculation': [
            ['月份', '算税地区','社保缴纳单位','公积金缴纳单位', '身份证号', '姓名', '成本所属项目','财务标准项目','财务标准单位', '薪酬类别',
             '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费',
             '失业保险费', '企业(职业)年金', '其它扣款', '调整收入', '调整扣除',
             '调整累计个税', '累计社保', '累计专项附加', '累计法定扣除',
             '累计调整扣除', '累计收入', '累计扣除', '累计应扣税款',
             '累计上次税款', '本次税款', '本次达到税率', '一次性年终奖校验','备注']
        ],
        'specialDeduction': [
            ['工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
             '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
             '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金']
        ],
        'remoteTax': [
            ['预留', '身份证号', '姓名', '一月', '二月', '三月', '四月', '五月', '六月',
             '七月', '八月', '九月', '十月', '十一月', '十二月']
        ],
        'idCard': [
            ['姓名', '身份证号','银行卡号','开户行名称','开户行编码','备注']
        ],
        'projectMapping': [
            ['人资项目名称', '标准项目名称', '标准项目编码','财务标准单位','财务标准单位编码']
        ],
        'subsidyPackage': [
            ['月份', '身份证号', '姓名', '成本所属项目', '财务标准项目', '工作餐补贴','交通补贴','通讯补贴','住房补贴','取证补贴','其他补贴','合计补贴','备注']
        ],
        'bonus': [
            ['缴纳月份', '算税地区',  '身份证号', '姓名', '成本所属项目', '财务标准项目', '薪酬类别', '原奖金金额','报税收入','税额','税率','备注']
        ],
        'outsourcingSalary': [
            ['月份','外包公司','姓名','身份证号','人资项目','财务标准项目','总金额','进项税','入账成本','薪酬类别','备注']
        ],
        'socialSecurityAndHousingFund': [
            ['月份','代缴单位','公积金金额','养老金额','医疗金额','补充医疗','失业金额','工伤金额','生育金额','年金','合计金额','备注']
        ],
        'socialSecurityAndHousingFundAdjustment': [
            ['月份','代缴单位','公积金金额','养老金额','医疗金额','补充医疗','失业金额','工伤金额','生育金额','年金','合计金额','备注']
        ]
    }
    
    return defaults.get(table_name, [[]])

@router.post("/api/calculate-social-security-allocation", response_model=SocialSecurityAllocationResponse)
async def calculate_social_security_allocation(request: SocialSecurityAllocationRequest):
    """计算社保公积金回摊"""
    try:
        tax_data = request.taxCalculationData
        social_data = request.socialSecurityData

        if not tax_data or len(tax_data) < 2:
            raise HTTPException(status_code=400, detail="算税底稿数据不足")

        if not social_data or len(social_data) < 2:
            raise HTTPException(status_code=400, detail="社保公积金数据不足")

        # 解析算税底稿表头
        tax_header = tax_data[0]
        tax_month_idx = tax_header.index('月份') if '月份' in tax_header else -1
        tax_id_idx = tax_header.index('身份证号') if '身份证号' in tax_header else -1
        tax_name_idx = tax_header.index('姓名') if '姓名' in tax_header else -1
        tax_project_idx = tax_header.index('成本所属项目') if '成本所属项目' in tax_header else -1
        tax_finance_project_idx = tax_header.index('财务标准项目') if '财务标准项目' in tax_header else -1
        tax_finance_unit_idx = tax_header.index('财务标准单位') if '财务标准单位' in tax_header else -1
        tax_salary_type_idx = tax_header.index('薪酬类别') if '薪酬类别' in tax_header else -1
        tax_income_idx = tax_header.index('本期收入') if '本期收入' in tax_header else -1
        tax_pension_idx = tax_header.index('基本养老保险费') if '基本养老保险费' in tax_header else -1
        tax_housing_idx = tax_header.index('住房公积金') if '住房公积金' in tax_header else -1
        tax_medical_idx = tax_header.index('基本医疗保险费') if '基本医疗保险费' in tax_header else -1
        tax_unemployment_idx = tax_header.index('失业保险费') if '失业保险费' in tax_header else -1
        tax_annuity_idx = tax_header.index('企业(职业)年金') if '企业(职业)年金' in tax_header else -1
        tax_current_tax_idx = tax_header.index('本次税款') if '本次税款' in tax_header else -1
        tax_social_unit_idx = tax_header.index('社保缴纳单位') if '社保缴纳单位' in tax_header else -1
        tax_housing_unit_idx = tax_header.index('公积金缴纳单位') if '公积金缴纳单位' in tax_header else -1
        tax_annuity_unit_idx = tax_header.index('年金缴纳单位') if '年金缴纳单位' in tax_header else -1

        # 解析社保公积金表头
        social_header = social_data[0]
        social_month_idx = social_header.index('月份') if '月份' in social_header else -1
        social_unit_idx = social_header.index('代缴单位') if '代缴单位' in social_header else -1
        social_personal_housing_idx = social_header.index('个人公积金') if '个人公积金' in social_header else -1
        social_personal_pension_idx = social_header.index('个人养老') if '个人养老' in social_header else -1
        social_personal_medical_idx = social_header.index('个人医疗') if '个人医疗' in social_header else -1
        social_personal_unemployment_idx = social_header.index('个人失业') if '个人失业' in social_header else -1
        social_personal_annuity_idx = social_header.index('个人年金') if '个人年金' in social_header else -1
        social_supplement_medical_idx = social_header.index('补充医疗') if '补充医疗' in social_header else -1
        social_company_housing_idx = social_header.index('单位公积金') if '单位公积金' in social_header else -1
        social_company_pension_idx = social_header.index('单位养老') if '单位养老' in social_header else -1
        social_company_medical_idx = social_header.index('单位医疗') if '单位医疗' in social_header else -1
        social_company_unemployment_idx = social_header.index('单位失业') if '单位失业' in social_header else -1
        social_company_injury_idx = social_header.index('单位工伤') if '单位工伤' in social_header else -1
        social_company_maternity_idx = social_header.index('单位生育') if '单位生育' in social_header else -1
        social_company_annuity_idx = social_header.index('企业年金') if '企业年金' in social_header else -1

        # 构建社保公积金映射 (月份+代缴单位 -> 各项费用)
        social_map = {}
        for row in social_data[1:]:
            if len(row) > max(social_month_idx, social_unit_idx):
                month = str(row[social_month_idx]) if social_month_idx >= 0 else ''
                unit = str(row[social_unit_idx]) if social_unit_idx >= 0 else ''
                key = f"{month}_{unit}"

                social_map[key] = {
                    'personal_housing': float(row[social_personal_housing_idx]) if social_personal_housing_idx >= 0 and row[social_personal_housing_idx] else 0,
                    'personal_pension': float(row[social_personal_pension_idx]) if social_personal_pension_idx >= 0 and row[social_personal_pension_idx] else 0,
                    'personal_medical': float(row[social_personal_medical_idx]) if social_personal_medical_idx >= 0 and row[social_personal_medical_idx] else 0,
                    'personal_unemployment': float(row[social_personal_unemployment_idx]) if social_personal_unemployment_idx >= 0 and row[social_personal_unemployment_idx] else 0,
                    'personal_annuity': float(row[social_personal_annuity_idx]) if social_personal_annuity_idx >= 0 and row[social_personal_annuity_idx] else 0,
                    'supplement_medical': float(row[social_supplement_medical_idx]) if social_supplement_medical_idx >= 0 and row[social_supplement_medical_idx] else 0,
                    'company_housing': float(row[social_company_housing_idx]) if social_company_housing_idx >= 0 and row[social_company_housing_idx] else 0,
                    'company_pension': float(row[social_company_pension_idx]) if social_company_pension_idx >= 0 and row[social_company_pension_idx] else 0,
                    'company_medical': float(row[social_company_medical_idx]) if social_company_medical_idx >= 0 and row[social_company_medical_idx] else 0,
                    'company_unemployment': float(row[social_company_unemployment_idx]) if social_company_unemployment_idx >= 0 and row[social_company_unemployment_idx] else 0,
                    'company_injury': float(row[social_company_injury_idx]) if social_company_injury_idx >= 0 and row[social_company_injury_idx] else 0,
                    'company_maternity': float(row[social_company_maternity_idx]) if social_company_maternity_idx >= 0 and row[social_company_maternity_idx] else 0,
                    'company_annuity': float(row[social_company_annuity_idx]) if social_company_annuity_idx >= 0 and row[social_company_annuity_idx] else 0,
                }

        # 构建回摊结果表头
        allocation_header = [
            '月份','社保缴纳单位','公积金缴纳单位','年金缴纳单位' ,'身份证号', '姓名', '成本所属项目','财务标准项目','财务标准单位', '薪酬类别',
            '本期收入', '个人养老回摊', '个人公积金回摊', '个人医疗回摊',
            '个人失业回摊', '个人年金回摊', '其它扣款', '包干费补贴', '本次个税',
            '单位养老分摊', '单位公积金分摊', '单位医疗分摊', '单位失业分摊',
            '单位工伤分摊', '单位生育分摊', '补充医疗分摊', '其他分摊',
            '总成本','备注'
        ]

        # 处理算税底稿数据，计算回摊
        allocation_data = [allocation_header]

        for row in tax_data[1:]:
            if len(row) <= max(tax_month_idx, tax_id_idx, tax_name_idx):
                continue

            month = str(row[tax_month_idx]) if tax_month_idx >= 0 else ''
            id_card = str(row[tax_id_idx]) if tax_id_idx >= 0 else ''
            name = str(row[tax_name_idx]) if tax_name_idx >= 0 else ''
            project = str(row[tax_project_idx]) if tax_project_idx >= 0 else ''
            finance_project = str(row[tax_finance_project_idx]) if tax_finance_project_idx >= 0 else ''
            finance_unit = str(row[tax_finance_unit_idx]) if tax_finance_unit_idx >= 0 else ''
            salary_type = str(row[tax_salary_type_idx]) if tax_salary_type_idx >= 0 else ''
            income = float(row[tax_income_idx]) if tax_income_idx >= 0 and row[tax_income_idx] else 0
            current_tax = float(row[tax_current_tax_idx]) if tax_current_tax_idx >= 0 and row[tax_current_tax_idx] else 0
            social_unit = str(row[tax_social_unit_idx]) if tax_social_unit_idx >= 0 else ''
            housing_unit = str(row[tax_housing_unit_idx]) if tax_housing_unit_idx >= 0 else ''
            annuity_unit = str(row[tax_annuity_unit_idx]) if tax_annuity_unit_idx >= 0 else ''

            # 查找对应的社保公积金数据
            social_key = f"{month}_{social_unit}"
            housing_key = f"{month}_{housing_unit}"
            annuity_key = f"{month}_{annuity_unit}"

            # 获取个人回摊金额
            personal_pension = social_map.get(social_key, {}).get('personal_pension', 0)
            personal_housing = social_map.get(housing_key, {}).get('personal_housing', 0)
            personal_medical = social_map.get(social_key, {}).get('personal_medical', 0)
            personal_unemployment = social_map.get(social_key, {}).get('personal_unemployment', 0)
            personal_annuity = social_map.get(annuity_key, {}).get('personal_annuity', 0)

            # 获取单位分摊金额
            company_pension = social_map.get(social_key, {}).get('company_pension', 0)
            company_housing = social_map.get(housing_key, {}).get('company_housing', 0)
            company_medical = social_map.get(social_key, {}).get('company_medical', 0)
            company_unemployment = social_map.get(social_key, {}).get('company_unemployment', 0)
            company_injury = social_map.get(social_key, {}).get('company_injury', 0)
            company_maternity = social_map.get(social_key, {}).get('company_maternity', 0)
            supplement_medical = social_map.get(social_key, {}).get('supplement_medical', 0)
            company_annuity = social_map.get(annuity_key, {}).get('company_annuity', 0)

            # 计算总成本
            total_cost = (income + personal_pension + personal_housing + personal_medical +
                         personal_unemployment + personal_annuity + current_tax +
                         company_pension + company_housing + company_medical +
                         company_unemployment + company_injury + company_maternity +
                         supplement_medical + company_annuity)

            # 构建回摊数据行
            allocation_row = [
                month, social_unit, housing_unit, annuity_unit, id_card, name, project, finance_project, finance_unit, salary_type,
                income, personal_pension, personal_housing, personal_medical,
                personal_unemployment, personal_annuity, 0, 0, current_tax,  # 其它扣款和包干费补贴暂设为0
                company_pension, company_housing, company_medical, company_unemployment,
                company_injury, company_maternity, supplement_medical, company_annuity,
                total_cost, ''  # 备注暂为空
            ]

            allocation_data.append(allocation_row)

        return SocialSecurityAllocationResponse(
            code=200,
            message=f"成功计算社保公积金回摊，共处理 {len(allocation_data) - 1} 条记录",
            data=allocation_data,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算社保公积金回摊失败: {str(e)}")

@router.post("/api/build-finance-template", response_model=BuildFinanceTemplateResponse)
async def build_finance_template(request: BuildFinanceTemplateRequest):
    """构建财务一体化计提发放模板"""
    try:
        adjustment_data = request.adjustmentData
        template_type = request.templateType

        if not adjustment_data or len(adjustment_data) < 2:
            raise HTTPException(status_code=400, detail="薪酬总表数据不足")

        # 解析薪酬总表表头
        header = adjustment_data[0]
        month_idx = header.index('月份') if '月份' in header else -1
        social_unit_idx = header.index('社保缴纳单位') if '社保缴纳单位' in header else -1
        housing_unit_idx = header.index('公积金缴纳单位') if '公积金缴纳单位' in header else -1
        annuity_unit_idx = header.index('年金缴纳单位') if '年金缴纳单位' in header else -1
        id_idx = header.index('身份证号') if '身份证号' in header else -1
        name_idx = header.index('姓名') if '姓名' in header else -1
        project_idx = header.index('成本所属项目') if '成本所属项目' in header else -1
        finance_project_idx = header.index('财务标准项目') if '财务标准项目' in header else -1
        finance_unit_idx = header.index('财务标准单位') if '财务标准单位' in header else -1
        salary_type_idx = header.index('薪酬类别') if '薪酬类别' in header else -1

        # 个人回摊字段索引
        personal_pension_idx = header.index('个人养老回摊') if '个人养老回摊' in header else -1
        personal_housing_idx = header.index('个人公积金回摊') if '个人公积金回摊' in header else -1
        personal_medical_idx = header.index('个人医疗回摊') if '个人医疗回摊' in header else -1
        personal_unemployment_idx = header.index('个人失业回摊') if '个人失业回摊' in header else -1
        personal_annuity_idx = header.index('个人年金回摊') if '个人年金回摊' in header else -1

        # 单位分摊字段索引
        company_pension_idx = header.index('单位养老分摊') if '单位养老分摊' in header else -1
        company_housing_idx = header.index('单位公积金分摊') if '单位公积金分摊' in header else -1
        company_medical_idx = header.index('单位医疗分摊') if '单位医疗分摊' in header else -1
        company_unemployment_idx = header.index('单位失业分摊') if '单位失业分摊' in header else -1
        company_injury_idx = header.index('单位工伤分摊') if '单位工伤分摊' in header else -1
        company_maternity_idx = header.index('单位生育分摊') if '单位生育分摊' in header else -1
        supplement_medical_idx = header.index('补充医疗分摊') if '补充医疗分摊' in header else -1
        company_annuity_idx = header.index('其他分摊') if '其他分摊' in header else -1

        # 按月份和缴纳单位汇总数据
        template_data = {}

        for row in adjustment_data[1:]:
            if len(row) <= max(month_idx, social_unit_idx, housing_unit_idx, annuity_unit_idx):
                continue

            month = str(row[month_idx]) if month_idx >= 0 else ''
            social_unit = str(row[social_unit_idx]) if social_unit_idx >= 0 else ''
            housing_unit = str(row[housing_unit_idx]) if housing_unit_idx >= 0 else ''
            annuity_unit = str(row[annuity_unit_idx]) if annuity_unit_idx >= 0 else ''

            # 社保数据汇总
            if social_unit and social_unit != '':
                social_key = f"{month}_{social_unit}"
                if social_key not in template_data:
                    template_data[social_key] = {
                        'month': month,
                        'unit': social_unit,
                        'type': 'social_security',
                        'personal_pension': 0,
                        'personal_medical': 0,
                        'personal_unemployment': 0,
                        'company_pension': 0,
                        'company_medical': 0,
                        'company_unemployment': 0,
                        'company_injury': 0,
                        'company_maternity': 0,
                        'supplement_medical': 0,
                    }

                template_data[social_key]['personal_pension'] += float(row[personal_pension_idx]) if personal_pension_idx >= 0 and row[personal_pension_idx] else 0
                template_data[social_key]['personal_medical'] += float(row[personal_medical_idx]) if personal_medical_idx >= 0 and row[personal_medical_idx] else 0
                template_data[social_key]['personal_unemployment'] += float(row[personal_unemployment_idx]) if personal_unemployment_idx >= 0 and row[personal_unemployment_idx] else 0
                template_data[social_key]['company_pension'] += float(row[company_pension_idx]) if company_pension_idx >= 0 and row[company_pension_idx] else 0
                template_data[social_key]['company_medical'] += float(row[company_medical_idx]) if company_medical_idx >= 0 and row[company_medical_idx] else 0
                template_data[social_key]['company_unemployment'] += float(row[company_unemployment_idx]) if company_unemployment_idx >= 0 and row[company_unemployment_idx] else 0
                template_data[social_key]['company_injury'] += float(row[company_injury_idx]) if company_injury_idx >= 0 and row[company_injury_idx] else 0
                template_data[social_key]['company_maternity'] += float(row[company_maternity_idx]) if company_maternity_idx >= 0 and row[company_maternity_idx] else 0
                template_data[social_key]['supplement_medical'] += float(row[supplement_medical_idx]) if supplement_medical_idx >= 0 and row[supplement_medical_idx] else 0

            # 公积金数据汇总
            if housing_unit and housing_unit != '':
                housing_key = f"{month}_{housing_unit}"
                if housing_key not in template_data:
                    template_data[housing_key] = {
                        'month': month,
                        'unit': housing_unit,
                        'type': 'housing_fund',
                        'personal_housing': 0,
                        'company_housing': 0,
                    }

                template_data[housing_key]['personal_housing'] += float(row[personal_housing_idx]) if personal_housing_idx >= 0 and row[personal_housing_idx] else 0
                template_data[housing_key]['company_housing'] += float(row[company_housing_idx]) if company_housing_idx >= 0 and row[company_housing_idx] else 0

            # 年金数据汇总
            if annuity_unit and annuity_unit != '':
                annuity_key = f"{month}_{annuity_unit}"
                if annuity_key not in template_data:
                    template_data[annuity_key] = {
                        'month': month,
                        'unit': annuity_unit,
                        'type': 'annuity',
                        'personal_annuity': 0,
                        'company_annuity': 0,
                    }

                template_data[annuity_key]['personal_annuity'] += float(row[personal_annuity_idx]) if personal_annuity_idx >= 0 and row[personal_annuity_idx] else 0
                template_data[annuity_key]['company_annuity'] += float(row[company_annuity_idx]) if company_annuity_idx >= 0 and row[company_annuity_idx] else 0

        # 构建财务一体化模板结果
        result_data = {
            'template_type': template_type,
            'year': request.year,
            'summary': template_data,
            'total_records': len(template_data),
            'generated_at': datetime.now().isoformat()
        }

        return BuildFinanceTemplateResponse(
            code=200,
            message=f"成功构建财务一体化计提发放模板，共汇总 {len(template_data)} 条记录",
            data=result_data,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"构建财务模板失败: {str(e)}")
