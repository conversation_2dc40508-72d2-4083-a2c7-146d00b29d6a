<template>
  <div class="sql-visual-view">
    <h2>SQL 可视化查询</h2>
    <el-form :model="queryForm" :inline="true" class="query-form">
      <el-form-item label="过账日期">
        <el-date-picker
          v-model="queryForm.postingDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="输入日期">
        <el-date-picker
          v-model="queryForm.inputDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="事由">
        <el-input v-model="queryForm.reason" placeholder="请输入事由" />
      </el-form-item>
      <el-form-item label="总账科目">
        <el-input v-model="queryForm.accountText" placeholder="请输入科目长文本" />
      </el-form-item>
      <el-form-item label="金额">
        <el-input v-model="queryForm.amount" placeholder="请输入金额" type="number" />
      </el-form-item>
      <el-form-item label="项目">
        <el-input v-model="queryForm.project" placeholder="请输入项目" />
      </el-form-item>
      <el-form-item label="单位">
        <el-input v-model="queryForm.unit" placeholder="请输入单位" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="query-results">
      <!-- 结果展示区域 -->
      <pre>{{ queryResults }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const queryForm = ref({
  postingDate: [],
  inputDate: [],
  reason: '',
  accountText: '',
  amount: '',
  project: '',
  unit: ''
})

const queryResults = ref('')

function handleQuery() {
  // 构造 SQL 查询：您可以根据需要自定义此逻辑
  const query = `SELECT * FROM some_table WHERE
    posting_date BETWEEN '${queryForm.value.postingDate[0]}' AND '${queryForm.value.postingDate[1]}' AND
    input_date BETWEEN '${queryForm.value.inputDate[0]}' AND '${queryForm.value.inputDate[1]}' AND
    reason LIKE '%${queryForm.value.reason}%' AND
    account_text LIKE '%${queryForm.value.accountText}%' AND
    amount  ${queryForm.value.amount} AND
    project LIKE '%${queryForm.value.project}%' AND
    unit LIKE '%${queryForm.value.unit}%'
  `
  // 更新查询结果：此处仅显示查询语句，实际实现需要发送给后端执行
  queryResults.value = query
}
</script>

<style scoped>
.sql-visual-view {
  padding: 20px;
  background-color: #f5f5f5;
}

.query-form {
  margin-bottom: 20px;
}

.query-results {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

